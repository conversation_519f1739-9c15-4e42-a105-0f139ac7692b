/**
 * 智能图片裁剪工具
 * 实现居中裁剪、智能缩放等功能
 */

class SmartImageCropper {
  constructor() {
    // 默认配置
    this.defaultConfig = {
      targetWidth: 295,    // 目标宽度（rpx转换为px）
      targetHeight: 413,   // 目标高度（rpx转换为px）
      quality: 0.9,        // 压缩质量
      format: 'jpeg',      // 输出格式
      cropMode: 'center'   // 裁剪模式：center(居中), top(顶部), bottom(底部)
    };
  }

  /**
   * 智能裁剪图片
   * @param {string} filePath - 图片文件路径
   * @param {Object} options - 裁剪选项
   * @returns {Promise<string>} - 裁剪后的base64字符串
   */
  async smartCrop(filePath, options = {}) {
    const config = { ...this.defaultConfig, ...options };
    
    try {
      console.log('开始智能裁剪:', { filePath, config });

      // 获取图片信息
      const imageInfo = await this.getImageInfo(filePath);
      console.log('原始图片信息:', imageInfo);

      // 计算裁剪参数
      const cropParams = this.calculateCropParams(imageInfo, config);
      console.log('裁剪参数:', cropParams);

      // 执行裁剪
      const croppedBase64 = await this.performCrop(filePath, cropParams, config);
      
      console.log('智能裁剪完成');
      return croppedBase64;

    } catch (error) {
      console.error('智能裁剪失败:', error);
      throw error;
    }
  }

  /**
   * 获取图片信息
   * @param {string} filePath - 图片路径
   * @returns {Promise<Object>} - 图片信息
   */
  getImageInfo(filePath) {
    return new Promise((resolve, reject) => {
      wx.getImageInfo({
        src: filePath,
        success: (res) => {
          resolve({
            width: res.width,
            height: res.height,
            path: res.path,
            orientation: res.orientation || 'up',
            type: res.type || 'unknown'
          });
        },
        fail: reject
      });
    });
  }

  /**
   * 计算裁剪参数
   * @param {Object} imageInfo - 图片信息
   * @param {Object} config - 配置
   * @returns {Object} - 裁剪参数
   */
  calculateCropParams(imageInfo, config) {
    const { width: imgWidth, height: imgHeight } = imageInfo;
    const { targetWidth, targetHeight, cropMode } = config;

    // 计算目标宽高比
    const targetRatio = targetWidth / targetHeight;
    const imageRatio = imgWidth / imgHeight;

    let cropWidth, cropHeight, cropX, cropY;

    if (imageRatio > targetRatio) {
      // 图片比目标更宽，需要裁剪宽度
      cropHeight = imgHeight;
      cropWidth = imgHeight * targetRatio;
      cropY = 0;
      
      // 根据裁剪模式确定X位置
      switch (cropMode) {
        case 'center':
          cropX = (imgWidth - cropWidth) / 2;
          break;
        case 'left':
          cropX = 0;
          break;
        case 'right':
          cropX = imgWidth - cropWidth;
          break;
        default:
          cropX = (imgWidth - cropWidth) / 2;
      }
    } else {
      // 图片比目标更高，需要裁剪高度
      cropWidth = imgWidth;
      cropHeight = imgWidth / targetRatio;
      cropX = 0;
      
      // 根据裁剪模式确定Y位置
      switch (cropMode) {
        case 'center':
          cropY = (imgHeight - cropHeight) / 2;
          break;
        case 'top':
          cropY = 0;
          break;
        case 'bottom':
          cropY = imgHeight - cropHeight;
          break;
        default:
          cropY = (imgHeight - cropHeight) / 2;
      }
    }

    return {
      cropX: Math.max(0, Math.floor(cropX)),
      cropY: Math.max(0, Math.floor(cropY)),
      cropWidth: Math.floor(cropWidth),
      cropHeight: Math.floor(cropHeight),
      outputWidth: targetWidth,
      outputHeight: targetHeight
    };
  }

  /**
   * 执行裁剪操作
   * @param {string} filePath - 图片路径
   * @param {Object} cropParams - 裁剪参数
   * @param {Object} config - 配置
   * @returns {Promise<string>} - 裁剪后的base64
   */
  performCrop(filePath, cropParams, config) {
    return new Promise((resolve, reject) => {
      try {
        const { cropX, cropY, cropWidth, cropHeight, outputWidth, outputHeight } = cropParams;
        
        // 创建canvas上下文
        const canvas = wx.createCanvasContext('smartCropCanvas');
        
        // 清空canvas
        canvas.clearRect(0, 0, outputWidth, outputHeight);
        
        // 绘制裁剪后的图片
        canvas.drawImage(
          filePath,
          cropX, cropY, cropWidth, cropHeight,  // 源图片裁剪区域
          0, 0, outputWidth, outputHeight       // 目标区域
        );
        
        // 绘制完成后导出
        canvas.draw(false, () => {
          wx.canvasToTempFilePath({
            canvasId: 'smartCropCanvas',
            x: 0,
            y: 0,
            width: outputWidth,
            height: outputHeight,
            destWidth: outputWidth,
            destHeight: outputHeight,
            quality: config.quality,
            fileType: config.format,
            success: (res) => {
              // 转换为base64
              this.fileToBase64(res.tempFilePath, config.format)
                .then(resolve)
                .catch(reject);
            },
            fail: reject
          });
        });
        
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 文件转base64
   * @param {string} filePath - 文件路径
   * @param {string} format - 图片格式
   * @returns {Promise<string>} - base64字符串
   */
  fileToBase64(filePath, format = 'jpeg') {
    return new Promise((resolve, reject) => {
      const mimeType = format === 'png' ? 'image/png' : 'image/jpeg';
      
      wx.getFileSystemManager().readFile({
        filePath,
        encoding: 'base64',
        success: (res) => {
          resolve(`data:${mimeType};base64,${res.data}`);
        },
        fail: reject
      });
    });
  }

  /**
   * 获取推荐的裁剪模式
   * @param {Object} imageInfo - 图片信息
   * @returns {string} - 推荐的裁剪模式
   */
  getRecommendedCropMode(imageInfo) {
    const { width, height } = imageInfo;
    const ratio = width / height;
    
    // 对于人像照片，通常使用居中或顶部裁剪
    if (ratio < 1) {
      // 竖向图片，推荐顶部裁剪（保留头部）
      // return 'top';
      return 'center';
    } else if (ratio > 1.5) {
      // 横向图片，推荐居中裁剪
      return 'center';
    } else {
      // 接近正方形，推荐居中裁剪
      return 'center';
    }
  }

  /**
   * 批量处理多张图片
   * @param {Array} filePaths - 图片路径数组
   * @param {Object} options - 处理选项
   * @returns {Promise<Array>} - 处理结果数组
   */
  async batchProcess(filePaths, options = {}) {
    const results = [];
    
    for (const filePath of filePaths) {
      try {
        const result = await this.smartCrop(filePath, options);
        results.push({ success: true, data: result, path: filePath });
      } catch (error) {
        results.push({ success: false, error: error.message, path: filePath });
      }
    }
    
    return results;
  }
}

// 创建全局实例
const smartImageCropper = new SmartImageCropper();

module.exports = smartImageCropper;
