# PDF分享功能同步调用方案

## 问题背景

遇到微信小程序API限制：
```
shareFileMessage:fail can only be invoked by user TAP gesture.
```

这是因为 `wx.shareFileMessage` 必须由用户的直接点击手势触发，不能在异步操作后调用。

## 新的解决方案

根据分析，`wx.shareFileMessage` 不能在任何回调函数中使用。新方案是：
1. 在用户点击事件的同步上下文中立即调用 `wx.shareFileMessage`
2. 同时启动 `wx.downloadFile` 在后台下载文件
3. 使用预期的文件路径进行分享，让微信在需要时读取文件

## 主要修改

### 1. 修改了 `pages/makeCreateResume/makeCreateResume.js` 中的 `handleGeneratePDF` 方法

**核心思路**：
- 在用户点击的同步上下文中立即调用 `wx.shareFileMessage`
- 同时启动 `wx.downloadFile` 在后台下载
- 使用预期的文件路径，让微信在分享时读取文件

**新的处理流程**：
1. 显示"正在准备分享..."的loading
2. 调用API生成PDF URL
3. 同时启动两个操作：
   - 启动 `wx.downloadFile` 下载文件到指定路径
   - 立即调用 `wx.shareFileMessage` 使用该路径
4. 根据分享结果显示相应提示

### 2. 关键代码实现

```javascript
// 先启动下载任务（不等待完成）
const downloadTask = wx.downloadFile({
  url: response.data.pdf_url,
  filePath: finalFilePath,
  success: (res) => {
    if (res.statusCode === 200) {
      console.log('PDF下载成功，文件路径:', res.filePath);
    }
  }
});

// 立即调用分享，使用预期的文件路径（在用户点击的同步上下文中）
wx.shareFileMessage({
  filePath: finalFilePath,
  fileName: fileName,
  success: () => {
    wx.showToast({
      title: '已发送给好友',
      icon: 'success'
    });
  },
  fail: (error) => {
    // 错误处理
  }
});
```

### 3. 用户体验改进

**分享成功**：
- 显示"已发送给好友"的成功提示

**分享失败**：
- 用户取消：显示"已取消分享"
- 网络错误：显示"分享失败，请检查网络连接"
- 其他错误：显示"分享失败，请重试"

## 测试步骤

### 前置条件
1. 确保微信小程序基础库版本 >= 2.16.1（wx.shareFileMessage API要求）
2. 必须在真机上测试（wx.shareFileMessage只能在真机上调试）
3. 确保有简历数据和模板

### 测试流程

1. **进入简历制作页面**
   - 打开微信小程序
   - 进入简历制作页面
   - 确保有简历数据显示

2. **点击下载按钮**
   - 点击工具栏中的"下载文档"按钮
   - 应该显示"正在准备分享..."的loading

3. **验证分享界面**
   - 等待PDF生成和下载完成
   - 应该直接弹出微信分享文件界面
   - 文件名应该显示为用户设置的简历名称.pdf

4. **测试分享成功**
   - 选择一个微信好友或群聊
   - 点击发送
   - 应该显示"已发送给好友"的成功提示

5. **测试分享取消**
   - 重复步骤1-3
   - 在分享界面点击取消
   - 应该显示"已取消分享"的提示

6. **测试网络异常**
   - 在网络不稳定的情况下测试
   - 应该显示相应的错误提示

### 预期结果

**如果新方案成功**：
- ✅ 点击下载按钮后直接弹出微信分享界面
- ✅ 分享的文件名正确显示（包含.pdf扩展名）
- ✅ 微信能够正确读取到下载的PDF文件
- ✅ 分享成功后显示成功提示
- ✅ 分享取消后显示取消提示

**可能的问题**：
- ❌ 如果文件还未下载完成，分享可能失败
- ❌ 仍然可能出现用户手势限制错误
- ⚠️ 需要测试文件下载和分享的时序问题

## 注意事项

1. **真机测试必要性**：`wx.shareFileMessage` API只能在真机上正常工作，开发者工具中会报错
2. **基础库版本**：确保微信小程序基础库版本不低于2.16.1
3. **文件格式**：确保文件名包含正确的.pdf扩展名，以便微信正确识别文件类型
4. **网络连接**：需要稳定的网络连接来下载PDF文件

## 回退方案

如果需要恢复原来的预览功能，可以将 `wx.shareFileMessage` 替换回 `wx.openDocument`：

```javascript
// 恢复预览功能的代码
wx.openDocument({
  filePath: res.filePath,
  fileType: 'pdf',
  showMenu: true,
  success: () => {
    resolve();
  },
  fail: (error) => {
    console.error('PDF打开失败:', error);
    wx.showToast({
      title: '文件打开失败，请重试',
      icon: 'none'
    });
    reject(error);
  }
});
```

## 相关文件

- `pages/makeCreateResume/makeCreateResume.js` - 主要业务逻辑修改
- `pages/makeCreateResume/components/toolBar/index.js` - 下载按钮触发事件
- `utils/api/resumeApi.js` - PDF生成API接口
