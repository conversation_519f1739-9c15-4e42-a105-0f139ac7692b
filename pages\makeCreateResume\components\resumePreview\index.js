// 引入API模块
const resumeApi = require('../../../../utils/api/resumeApi');

Component({
  properties: {
    resumeData: {
      type: Object,
      value: {},
      observer: function(newVal) {
        if (!newVal) return;

        this.setData({
          currentResumeData: newVal
        });
      }
    },
    template: {
      type: Object,
      value: {
        id: 'templateA01',
        name: '模板一'
      },
      observer: function(newVal) {
        if (newVal && newVal.id) {
          this.setData({
            currentTemplate: newVal.id
          });
        }
      }
    },
    config: {
      type: Object,
      value: {
        themeColor: '#2B6CB0',
        fontSize: '11',
        spacing: '1.2'
      },
      observer: function(newVal, oldVal) {
        if (!newVal || !oldVal) return;

        const needUpdate =
          newVal.themeColor !== oldVal.themeColor ||
          newVal.fontSize !== oldVal.fontSize ||
          newVal.spacing !== oldVal.spacing;

        if (needUpdate) {
          this.updateStyle(newVal);
        }
      }
    }
  },

  data: {
    currentTemplate: 'templateA01',
    currentResumeData: {},
    customStyle: '',
    lastConfig: null,
    // 图片预览相关
    previewImageUrl: '',
    imageLoading: false,
    imageError: false
  },

  methods: {
    updateStyle(config) {
      if (!config) return;

      let fontSize = '14pt';
      if (config.fontSize) {
        const size = parseInt(config.fontSize.toString().replace('px', ''));
        fontSize = size + 'pt';
      }

      const style = `
        --theme-color: ${config.themeColor || '#4B6CB0'};
        --font-size: ${fontSize};
        --spacing: ${config.spacing || 1};
      `;

      this.setData({
        customStyle: style,
        lastConfig: Object.assign({}, config)
      });

      const templateId = this.data.currentTemplate;
      const template = this.selectComponent(`#${templateId}`);
      if (template) {
        template.updateStyle(Object.assign({}, config, {
          fontSize: fontSize
        }));
      }
    },

    getResumeRenderData() {
      return {
        template: {
          id: this.data.currentTemplate,
          styles: {}
        },
        resumeData: this.data.currentResumeData,
        config: this.data.lastConfig || this.properties.config,
        customStyle: this.data.customStyle
      };
    },

    // 简单的预览图片请求方法（父页面调用）
    debounceRequestPreviewImage() {
      this.requestPreviewImage();
    },

    // 请求服务端生成预览图片
    requestPreviewImage() {
      const currentTemplate = this.data.currentTemplate;
      const currentResumeData = this.data.currentResumeData;
      const lastConfig = this.data.lastConfig;
      if (!currentTemplate || !currentResumeData || !lastConfig) {
        return;
      }

      this.setData({
        imageLoading: true,
        imageError: false,
        previewImageUrl: ''
      });

      // 调用API生成预览图片
      resumeApi.generatePreviewImage(currentResumeData, lastConfig, currentTemplate)
        .then(response => {

          if (response.success && response.data && response.data.image_url) {
            console.log('设置图片URL:', response.data.image_url);

            // 临时测试：如果是本地IP，提示用户配置域名白名单
            const imageUrl = response.data.image_url;
            // if (imageUrl.includes('192.168.') || imageUrl.includes('localhost') || imageUrl.includes('127.0.0.1')) {
            //   console.warn('⚠️ 检测到本地IP图片URL，可能需要配置域名白名单');
            //   console.warn('请在微信开发者工具中：详情 -> 本地设置 -> 勾选"不校验合法域名"');
            // }

            this.setData({
              previewImageUrl: imageUrl,
              imageLoading: false,
              imageError: false
            });
          } else {
            console.error('服务器返回数据格式错误:', response);
            throw new Error('服务器返回数据格式错误');
          }
        })
        .catch(error => {
          console.error('预览图片生成失败:', error);
          this.setData({
            imageLoading: false,
            imageError: true,
            previewImageUrl: ''
          });
        });
    },

    // 图片加载成功
    onImageLoad() {
      this.setData({ imageError: false });
    },

    // 图片加载失败
    onImageError(e) {
      console.error('预览图片加载失败:', e);
      console.error('当前图片URL:', this.data.previewImageUrl);

      // 检查是否是域名白名单问题
      const imageUrl = this.data.previewImageUrl || '';
      if (imageUrl.includes('192.168.') || imageUrl.includes('localhost') || imageUrl.includes('127.0.0.1')) {
        console.error('🚨 图片加载失败可能是域名白名单问题！');
        console.error('解决方案：微信开发者工具 -> 详情 -> 本地设置 -> 勾选"不校验合法域名"');
      }

      this.setData({
        imageError: true,
        imageLoading: false
      });
    },

    // 重新生成预览
    retryGeneratePreview() {
      this.requestPreviewImage();
    }
  }
});
